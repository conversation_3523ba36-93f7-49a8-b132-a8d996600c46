class SearchParams {
  final String type;
  final String term;
  final String retailOutletId;

  const SearchParams({
    this.type = 'retailproducts',
    required this.term,
    required this.retailOutletId,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'term': term,
      'retailOutletId': retailOutletId,
    };
  }

  SearchParams copyWith({
    String? type,
    String? term,
    String? retailOutletId,
  }) {
    return SearchParams(
      type: type ?? this.type,
      term: term ?? this.term,
      retailOutletId: retailOutletId ?? this.retailOutletId,
    );
  }
}

class SearchOrderParams {
  final String searchTerm;
  final String type;
  final SearchQuery query;

  const SearchOrderParams({
    required this.searchTerm,
    this.type = "orders",
    required this.query,
  });

  Map<String, dynamic> toMap() {
    return {
      'searchTerm': searchTerm,
      'type': type,
      'query': query.toMap(),
    };
  }
}

class SearchQuery {
  final int limit;

  const SearchQuery({this.limit = 10});

  Map<String, dynamic> toMap() {
    return {
      'limit': limit,
    };
  }
}
