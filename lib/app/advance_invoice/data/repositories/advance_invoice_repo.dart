import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/data/sources/advance_invoice_data_source.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/create_retail_invoice_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/search_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/upload_pod_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final advanceInvoiceRepoProvider =
    Provider.autoDispose<AdvanceInvoiceRepo>((ref) {
  final advanceInvoiceDataSource = ref.read(advanceInvoiceDataSourceProvider);
  return AdvanceInvoiceRepoImplementation(advanceInvoiceDataSource, ref);
});

abstract class AdvanceInvoiceRepo {
  Future<ApiResponse<AdvanceInvoiceSettings>> fetchAdvanceInvoiceSettings();
  Future<ApiResponse<void>> createAdditionalCharge(AdditionalCharge params);
  Future<ApiResponse<void>> createTaxRate(TaxRate params);
  Future<ApiResponse<RetailInvoice>> createRetailInvoice(
      CreateRetailInvoiceParam params);
  Future<ApiResponse<RetailInvoice>> fetchAdvanceInvoice(String id);
  Future<ApiResponse<void>> deleteAdvanceInvoice(String id);
  Future<ApiResponse<FetchAdvanceInvoicesResponse>> fetchAdvanceInvoices(
      FetchAdvanceInvoiceParams params);
  Future<ApiResponse<void>> uploadAdvanceInvoiceDoc(String id, String doc);
  Future<ApiResponse<void>> uploadPod(UploadPodParams params);
  Future<ApiResponse<void>> uploadLogo(String imageInBase64);
  Future<ApiResponse<LoanContract>> getLoanContract();
  Future<ApiResponse<List<RetailOutlet>>> searchMerchant(String searchTerm);
  Future<ApiResponse<List<WalletBank>>> getBankAccounts();
  Future<ApiResponse<List<SettlementBank>>> getSettlementBanks();
  Future<ApiResponse<(String bankName, String? bvn)>> validateBankAccount(
      String accountNumber, String bankCode);
  Future<ApiResponse<bool>> linkBankAccount(LinkBankParams params);
  Future<ApiResponse<List<Variant>>> searchRetailProducts(SearchParams params);
  Future<ApiResponse<List<Order>>> searchOrders(SearchOrderParams params);
}

class AdvanceInvoiceRepoImplementation implements AdvanceInvoiceRepo {
  final AdvanceInvoiceDataSource _advanceInvoiceDataSource;
  final Ref _ref;

  AdvanceInvoiceRepoImplementation(this._advanceInvoiceDataSource, this._ref);

  @override
  Future<ApiResponse<AdvanceInvoiceSettings>> fetchAdvanceInvoiceSettings() {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.fetchAdvanceInvoiceSettings(), _ref);
  }

  @override
  Future<ApiResponse<void>> createAdditionalCharge(AdditionalCharge params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.createAdditionalCharge(params), _ref);
  }

  @override
  Future<ApiResponse<void>> createTaxRate(TaxRate params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.createTaxRate(params), _ref);
  }

  @override
  Future<ApiResponse<RetailInvoice>> createRetailInvoice(
      CreateRetailInvoiceParam params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.createRetailInvoice(params), _ref);
  }

  @override
  Future<ApiResponse<RetailInvoice>> fetchAdvanceInvoice(String id) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.fetchAdvanceInvoice(id), _ref);
  }

  @override
  Future<ApiResponse<void>> deleteAdvanceInvoice(String id) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.deleteAdvanceInvoice(id), _ref);
  }

  @override
  Future<ApiResponse<FetchAdvanceInvoicesResponse>> fetchAdvanceInvoices(
      FetchAdvanceInvoiceParams params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.fetchAdvanceInvoices(params), _ref);
  }

  @override
  Future<ApiResponse<void>> uploadAdvanceInvoiceDoc(String id, String doc) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.uploadAdvanceInvoiceDoc(id, doc), _ref);
  }

  @override
  Future<ApiResponse<void>> uploadPod(UploadPodParams params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.uploadPod(params), _ref);
  }

  @override
  Future<ApiResponse<void>> uploadLogo(String imageInBase64) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.uploadLogo(imageInBase64), _ref);
  }

  @override
  Future<ApiResponse<LoanContract>> getLoanContract() {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.getLoanContract(), _ref);
  }

  @override
  Future<ApiResponse<List<RetailOutlet>>> searchMerchant(String searchTerm) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.searchMerchant(searchTerm), _ref);
  }

  @override
  Future<ApiResponse<List<WalletBank>>> getBankAccounts() {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.getBankAccounts(), _ref);
  }

  @override
  Future<ApiResponse<List<SettlementBank>>> getSettlementBanks() {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.getSettlementBanks(), _ref);
  }

  @override
  Future<ApiResponse<(String bankName, String? bvn)>> validateBankAccount(
      String accountNumber, String bankCode) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.validateBankAccount(
            accountNumber, bankCode),
        _ref);
  }

  @override
  Future<ApiResponse<bool>> linkBankAccount(LinkBankParams params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.linkBankAccount(params), _ref);
  }

  @override
  Future<ApiResponse<List<Variant>>> searchRetailProducts(SearchParams params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.searchRetailProducts(params), _ref);
  }

  @override
  Future<ApiResponse<List<Order>>> searchOrders(SearchOrderParams params) {
    return dioInterceptor(
        () => _advanceInvoiceDataSource.searchOrders(params), _ref);
  }
}
