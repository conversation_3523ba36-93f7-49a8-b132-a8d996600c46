import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/advance_invoice_summary_table.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/upload_pod_widget.dart';
import 'package:td_procurement/app/order/presentation/widgets/order_action_bar.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class AdvanceInvoiceSummaryScreen extends ConsumerStatefulWidget {
  final String invoiceId;
  final RetailInvoice? invoice;

  const AdvanceInvoiceSummaryScreen(this.invoiceId, {super.key, this.invoice});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _AdvanceInvoiceSummaryScreen();
  }
}

class _AdvanceInvoiceSummaryScreen
    extends ConsumerState<AdvanceInvoiceSummaryScreen> {
  final isPrintingInvoicePdf = ValueNotifier<bool>(false);
  final isUploadingPod = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    fetchInvoice();
  }

  @override
  void dispose() {
    isPrintingInvoicePdf.dispose();
    isUploadingPod.dispose();
    super.dispose();
  }

  void fetchInvoice({bool forced = false}) {
    // Only fetch if invoice is not provided
    if (widget.invoice == null || forced) {
      Future.microtask(() {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchInvoiceDetails(widget.invoiceId, forced: forced);
      });
    } else {
      // Set the provided invoice in the state
      Future.microtask(() {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .setAdvanceInvoiceDetails(widget.invoice!);
      });
    }
  }

  AsyncValue<RetailInvoice?> get invoiceData =>
      ref.watch(advanceInvoiceControllerProvider).advanceInvoiceDetails;

  @override
  Widget build(BuildContext context) {
    return invoiceData.when(
      data: (data) => _dataWidget(data),
      error: (error, stackTrace) =>
          FailureWidget(e: error, retry: fetchInvoice),
      loading: () => Skeletonizer(
        enabled: true,
        child: _dataWidget(RetailInvoice.defaultValue()),
      ),
    );
  }

  Widget _dataWidget(RetailInvoice? invoice) {
    final textTheme = Theme.of(context).textTheme;

    if (invoice == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildActionBar(invoice),
        const Gap(20),
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Invoice #${invoice.invoiceNumber}',
                    style: textTheme.headlineMedium,
                  ),
                  const Gap(10),
                  Row(
                    children: [
                      Text(
                        'Created: ${invoice.createdAt.toDate()}',
                        style: textTheme.bodyMedium
                            ?.copyWith(color: Palette.blackSecondary),
                      ),
                      const Gap(10),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(invoice.status)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          invoice.status,
                          style: textTheme.bodySmall?.copyWith(
                            color: _getStatusColor(invoice.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (invoice.dueDate != null)
                    Text(
                      'Due:        ${invoice.dueDate?.toDate()}',
                      style: textTheme.bodyMedium
                          ?.copyWith(color: Palette.blackSecondary),
                    ),
                  const Gap(20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Deliver To',
                          style: textTheme.bodyMedium?.copyWith(
                            color: Palette.blackSecondary,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 6,
                        child: Text(
                          invoice.shippingAddress.address1 ?? '',
                          style: textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w500),
                        ),
                      ),
                    ],
                  ),
                  const Gap(20),
                  AdvanceInvoiceSummaryTableWidget(invoice),
                  const Gap(20),
                  if (invoice.showPaymentDetail)
                    Container(
                      decoration: BoxDecoration(
                          border: Border.all(color: Palette.kE7E7E7),
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: const [
                            BoxShadow(
                                offset: Offset(0, 4),
                                blurRadius: 4,
                                spreadRadius: 0,
                                color: Palette.k0000000A)
                          ]),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'How to pay',
                            style: textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Palette.primary),
                          ),
                          const Gap(10),
                          Text.rich(
                            TextSpan(
                              text: 'Pay '
                                  '${CurrencyWidget.value(context, invoice.currency.iso!, invoice.total)}',
                              children: [
                                TextSpan(
                                  text: ' with bank transfer',
                                  style: textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 18),
                                )
                              ],
                            ),
                            style: textTheme.headlineSmall,
                          ),
                          Text(
                            'Transfer funds to the following bank account to complete payment for this invoice',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.blackSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Divider(
                            height: 50,
                            thickness: 1,
                            color: Palette.kE7E7E7,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Account Number',
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w600),
                              ),
                              Text(
                                invoice.bankAccount.accountNumber ?? '',
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                            ],
                          ),
                          const Gap(10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Bank Name',
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w600),
                              ),
                              Text(
                                invoice.bankAccount.bankName ?? '',
                                style: textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                            ],
                          )
                        ],
                      ),
                    )
                ],
              ),
            ),
          ),
        ),
        const Gap(40),
      ],
    );
  }

  Widget _buildActionBar(RetailInvoice invoice) {
    return MultiValueListenableBuilder<bool, bool, Null>(
      valueListenable1: isPrintingInvoicePdf,
      valueListenable2: isUploadingPod,
      builder: (context, isPrintingPdf, isUploadingPod, _, __) {
        return OrderActionBarWidget(
          leftText: 'Advance Invoice Summary',
          isCloseIcon: false,
          leftIconAction: () => context.pop(),
          rightButton1Text: 'Print',
          rightButton2Text: invoice.requirePodUpload ? 'Upload POD' : null,
          rightButton1Loading: isPrintingPdf,
          rightButton2Loading: isUploadingPod,
          rightButton1Action: () => _handleInvoiceDownload(null),
          rightButton2Action:
              invoice.requirePodUpload ? _handlePodUpload : null,
        );
      },
    );
  }

  void _handleInvoiceDownload(String? url) async {
    isPrintingInvoicePdf.value = true;
    if (mounted) {
      if (url != null) {
        openUri(url);
        isPrintingInvoicePdf.value = false;
      } else {
        isPrintingInvoicePdf.value = false;
        Toast.error('No invoice URL available', context);
      }
    }
  }

  void _handlePodUpload() {
    final invoice =
        ref.read(advanceInvoiceControllerProvider).advanceInvoiceDetails.value;
    if (invoice != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => UploadPodWidget(
          invoiceId: invoice.id,
          onPodUploaded: () {
            fetchInvoice(forced: true);

            // reload the advance invoice list
            final params = ref
                .read(advanceInvoiceControllerProvider)
                .fetchAdvanceInvoiceParams;
            ref
                .read(advanceInvoiceControllerProvider.notifier)
                .fetchAdvanceInvoices(params, forced: true);
          },
        ),
      );

      // showCustomGeneralDialog(
      //   context,
      //   child: UploadPodWidget(invoiceId: invoice.id),
      //   percentage: 0.4,
      //   minRightSectionWidth: 520,
      // );
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'accepted':
        return Colors.green.shade600;
      case 'pending':
        return Colors.amber.shade700;
      case 'processing':
        return Colors.blue.shade600;
      case 'completed':
        return Colors.grey.shade700;
      default:
        return Colors.grey;
    }
  }
}
