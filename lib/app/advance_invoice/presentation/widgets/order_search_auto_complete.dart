import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/search_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

/// Provider for found orders
final foundOrdersProvider = StateProvider<List<Order>>((_) => []);

/// Provider for selected order
final selectedOrderProvider = StateProvider<Order?>((_) => null);

class OrderSearchAutoCompleteWidget extends ConsumerStatefulWidget {
  const OrderSearchAutoCompleteWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      OrderSearchAutoCompleteWidgetState();
}

class OrderSearchAutoCompleteWidgetState
    extends ConsumerState<OrderSearchAutoCompleteWidget>
    with SingleTickerProviderStateMixin {
  final _orderController = TextEditingController();
  final _searchNotifier = ValueNotifier<bool>(false);
  final _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  Timer? _debounce;
  late AnimationController _controller;
  late Animation<double> _opacity;
  bool _showOrderAutocomplete = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _orderController.dispose();
    _searchNotifier.dispose();
    _debounce?.cancel();
    _controller.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _controller.reset();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final selectedOrder = ref.watch(selectedOrderProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(textTheme),
        const Gap(8),
        if (selectedOrder != null)
          _buildSelectedOrderTile(selectedOrder, textTheme)
        else
          _buildSearchField(textTheme),
        const Gap(10),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Text(
      'Search Order',
      style: textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: Palette.primaryBlack,
      ),
    );
  }

  Widget _buildSelectedOrderTile(Order order, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Palette.primaryBlack.withValues(alpha: 0.04),
      ),
      child: Row(
        children: [
          // Order icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Palette.primaryBlack.withValues(alpha: 0.08),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.receipt_long,
              color: Palette.primaryBlack.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
          const Gap(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Selected Order',
                      style: textTheme.bodySmall?.copyWith(
                        color: Palette.blackSecondary,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    const Gap(8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Palette.primaryBlack.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${(order.items ?? []).length} items',
                        style: textTheme.bodySmall?.copyWith(
                          color: Palette.primaryBlack.withValues(alpha: 0.8),
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const Gap(4),
                Text(
                  order.orderNumber ?? order.customerName ?? 'Order',
                  style: textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Palette.primaryBlack,
                    fontSize: 16,
                  ),
                ),
                if (order.customerName != null) ...[
                  const Gap(2),
                  Text(
                    order.customerName!,
                    style: textTheme.bodyMedium?.copyWith(
                      color: Palette.blackSecondary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const Gap(8),
          // Remove button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                ref.read(selectedOrderProvider.notifier).state = null;
                _orderController.clear();
              },
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Palette.primaryBlack.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.close,
                  color: Palette.primaryBlack.withValues(alpha: 0.6),
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(TextTheme textTheme) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _orderController,
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w400,
          color: Palette.primaryBlack,
        ),
        onTap: () {
          if (_orderController.text.isNotEmpty && _showOrderAutocomplete) {
            _updateOverlay();
          }
        },
        onChanged: (text) {
          if (text.isNotEmpty) {
            if (_debounce?.isActive ?? false) _debounce?.cancel();
            _debounce = Timer(const Duration(milliseconds: 500), () {
              handleSearch(text);
            });
          } else {
            _removeOverlay();
            setState(() {
              _showOrderAutocomplete = false;
            });
          }
        },
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Palette.stroke),
            borderRadius: BorderRadius.circular(6),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Palette.primary),
            borderRadius: BorderRadius.circular(6),
          ),
          hintText: 'Search by order number',
          hintStyle: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            color: Palette.placeholder,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          suffixIcon: ValueListenableBuilder<bool>(
            valueListenable: _searchNotifier,
            builder: (context, isLoading, _) {
              return isLoading
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(Palette.primary),
                          ),
                        ),
                        const Gap(8),
                      ],
                    )
                  : const SizedBox.shrink();
            },
          ),
          suffixIconConstraints: BoxConstraints.tight(const Size(30, 20)),
          constraints: const BoxConstraints.tightFor(height: 34),
        ),
      ),
    );
  }

  Future<void> handleSearch(String searchTerm) async {
    _removeOverlay();
    _searchNotifier.value = true;
    await _searchOrders(searchTerm);
    _searchNotifier.value = false;

    setState(() {
      _showOrderAutocomplete = searchTerm.isNotEmpty;
    });

    _updateOverlay();
  }

  Future<List<Order>> _searchOrders(String searchTerm) async {
    List<Order> results = [];

    final params = SearchOrderParams(
      searchTerm: searchTerm,
      query: const SearchQuery(limit: 10),
    );

    final res = await ref.read(searchOrdersUseCaseProvider(params));

    res.when(
      success: (data) {
        results = data;
        ref.read(foundOrdersProvider.notifier).state = data;
      },
      failure: (error, _) {
        // Handle error silently for now
        ref.read(foundOrdersProvider.notifier).state = [];
      },
    );
    return results;
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final searchedOrders = ref.watch(foundOrdersProvider);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          if (_showOrderAutocomplete &&
              (_controller.status == AnimationStatus.forward ||
                  _controller.status == AnimationStatus.completed))
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay,
              ),
            ),
          Positioned(
            left: offset.dx,
            top: offset.dy + size.height + 4,
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, size.height + 4),
              child: FadeTransition(
                opacity: _opacity,
                child: Material(
                  elevation: 4.0,
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        )
                      ],
                    ),
                    child: searchedOrders.isNotEmpty
                        ? ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: const BouncingScrollPhysics(),
                            itemCount: searchedOrders.length,
                            itemBuilder: (context, index) {
                              final order = searchedOrders[index];
                              return HoverableContainer(
                                index: index,
                                child: ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Palette.primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Icon(
                                      Icons.receipt_long,
                                      color: Palette.primary,
                                      size: 16,
                                    ),
                                  ),
                                  title: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          order.orderNumber ?? 'Order',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Palette.statusSuccess
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          '${(order.items ?? []).length} items',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.copyWith(
                                                color: Palette.statusSuccess,
                                                fontSize: 10,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  subtitle: order.customerName != null
                                      ? Text(
                                          order.customerName!,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w400,
                                                color: Palette.blackSecondary,
                                              ),
                                        )
                                      : null,
                                  onTap: () {
                                    _removeOverlay();
                                    ref
                                        .read(selectedOrderProvider.notifier)
                                        .state = order;
                                    _orderController.text = order.orderNumber ??
                                        order.customerName ??
                                        '';
                                  },
                                ),
                              );
                            },
                          )
                        : Container(
                            padding: const EdgeInsets.all(16),
                            child: Text(
                              'No orders found',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Palette.blackSecondary,
                                  ),
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _updateOverlay() {
    _removeOverlay();

    if (_showOrderAutocomplete) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      _controller.forward();
    }
  }
}
