import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/logo_upload_widget.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/order/presentation/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/components/widgets/streamed_image.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class AdvanceInvoicePreviewWidget extends ConsumerWidget {
  const AdvanceInvoicePreviewWidget({super.key, required this.randomNumber});

  final String randomNumber;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = context.textTheme;
    final settings = ref.watch(advanceInvoiceControllerProvider).settings;
    final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;
    final selectedCustomer = ref.watch(selectedCustomerProvider);
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final lineItems = createParams.lineItems;
    final taxes = createParams.taxes ?? [];
    final charges = createParams.charges ?? [];
    final billTo = selectedCustomer;
    final shipTo = selectedCustomer;
    final invoiceNumber = 'IVN-$randomNumber';
    final dueDate = createParams.dueDate;
    final subtotal =
        lineItems.fold<double>(0, (sum, item) => sum + (item.total));
    final subTotalWithTaxAndDiscount = lineItems.fold<double>(
        0, (sum, item) => sum + (item.totalWithTaxAndDiscount));
    final discount = createParams.discount;
    final note = createParams.note;
    final bankAccount = createParams.settlementBank;

    // Calculate taxes
    final taxAmount = taxes.fold<double>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charges
    final chargeAmount = charges.fold<double>(0, (sum, charge) {
      return sum + (charge.amount ?? 0);
    });

    // Calculate discount amount
    double discountAmount = 0;
    if (discount != null) {
      if (discount.type == DiscountType.fixed) {
        discountAmount = discount.value.toDouble();
      } else if (discount.type == DiscountType.percentage) {
        discountAmount = subtotal * (discount.value.toDouble() / 100);
      }
    }

    final total =
        subTotalWithTaxAndDiscount + taxAmount + chargeAmount - discountAmount;

    final currencyCode = ref.read(currencyCodeProvider);

    return Container(
      color: Palette.kFCFCFC,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8)),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 10)],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 40.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header row: Invoice, logo
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left: Invoice title and meta
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Invoice',
                            style: context.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: Palette.primaryBlack,
                            )),
                        const Gap(24),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Invoice number',
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        color: Colors.black54,
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.3,
                                      )),
                                  Text(invoiceNumber,
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      )),
                                ],
                              ),
                            ),
                            if (dueDate != null)
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Date due',
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                          color: Colors.black54,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: 0.3,
                                        )),
                                    Text(dueDate.toDate(),
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        )),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Right: Logo
                  Container(
                    width: 64,
                    height: 64,
                    margin: const EdgeInsets.only(left: 24),
                    child: settings.when(
                      data: (data) {
                        if (data == null || (data.logoUrl?.isEmpty ?? true)) {
                          return Material(
                            child: InkWell(
                              onTap: () => uploadBrandImage(context),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Palette.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        Palette.primary.withValues(alpha: 0.5),
                                    width: 1,
                                  ),
                                ),
                                child: Icon(Icons.add_photo_alternate,
                                    size: 40, color: Palette.placeholder),
                              ),
                            ),
                          );
                        }
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child:
                              StreamedImageWidget(imageUrl: data.logoUrl ?? ''),
                        ).onTap(
                            () => uploadBrandImage(context, updating: true));
                      },
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          color: Palette.kE7E7E7.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.add_photo_alternate,
                            size: 40, color: Palette.kE7E7E7),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Company, Bill to, Ship to
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Company info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(outlet?.outletBusinessName?.toTitleCase() ?? '-',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.2,
                            )),
                        Text(outlet?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.contactPhone ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                  Gap(5.w),
                  // Bill to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Bill to',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                            )),
                        Text(billTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.contactPhone ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                  Gap(5.w),
                  // Ship to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Ship to',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                            )),
                        Text(shipTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            )),
                        Text(shipTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(shipTo?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.phoneNumber ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Amount due and pay online
              Text.rich(
                TextSpan(
                    text: CurrencyWidget.value(context, currencyCode, total),
                    children: [
                      if (dueDate != null)
                        TextSpan(
                          text: ' due ${dueDate.toDate()}',
                          style: context.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w800,
                            letterSpacing: 1.6,
                          ),
                        ),
                    ]),
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w800,
                ),
              ),
              const Gap(32),
              // Items table
              _buildPreviewItemsTable(context, lineItems, currencyCode),
              const Gap(32),
              // Subtotal, shipping, total, amount due
              Row(
                children: [
                  Expanded(child: Container()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildSummaryRow(context, 'Subtotal',
                            subTotalWithTaxAndDiscount, currencyCode),
                        if (taxes.isNotEmpty) ...[
                          ...taxes.map((tax) => _buildSummaryRow(
                              context,
                              '${tax.name} (${tax.rate}%)',
                              (subtotal.toDouble() *
                                      ((tax.rate?.toDouble() ?? 0.0) / 100.0))
                                  .toDouble(),
                              currencyCode)),
                        ],
                        if (charges.isNotEmpty) ...[
                          ...charges.map((charge) => _buildSummaryRow(
                              context,
                              charge.name,
                              charge.amount?.toDouble() ?? 0,
                              currencyCode)),
                        ],
                        if (discountAmount > 0)
                          _buildSummaryRow(context, 'Discount', discountAmount,
                              currencyCode),
                        _buildSummaryRow(context, 'Total', total, currencyCode),
                        const Gap(8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Amount Due',
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.2,
                                )),
                            const Gap(8),
                            Text(
                                CurrencyWidget.value(
                                    context, currencyCode, total),
                                style: context.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: 0.3,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Note
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: note != null && note.isNotEmpty
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('note'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                              note,
                              style: context.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.1,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: bankAccount != null
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('bank-details'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                                'Account Name: ${bankAccount.accountName != null ? toTitleCase(bankAccount.accountName) : '-'}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                            Text(
                                'Account Number: ${bankAccount.accountNumber ?? '-'}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                            Text('Bank Name: ${bankAccount.bankName}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              const Gap(32),
              // Footer summary
              Divider(
                color: Palette.kE7E7E7,
                height: 0,
                // indent: 0,
              ),
              const Gap(4),
              Text('$invoiceNumber ${dueDate != null ? '· due $dueDate' : ''}',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: Colors.black54,
                    letterSpacing: 0.2,
                  )),
            ],
          ),
        ),
      ),
    );
  }

  void uploadBrandImage(BuildContext context, {bool updating = false}) {
    showDialog(
      context: context,
      builder: (context) => LogoUploadWidget(updating: updating),
    );
  }

  Widget _buildPreviewItemsTable(
      BuildContext context, List<LineItemParam> lineItems, String currency) {
    final textTheme = context.textTheme;
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Table(
                columnWidths: const {
                  0: FlexColumnWidth(2.5), // Name
                  1: FlexColumnWidth(2), // Qty
                  2: FlexColumnWidth(2), // Unit price
                  3: FlexColumnWidth(2), // Tax rate
                  4: FlexColumnWidth(2), // Discount
                  5: FlexColumnWidth(2.5), // Amount
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: [
                  TableRow(
                    decoration: const BoxDecoration(color: Color(0xFFF5F5F5)),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Name',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Qty',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Unit\nPrice (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Tax',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Discount',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Total (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                    ],
                  ),
                  ...lineItems.map((item) {
                    return TableRow(children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.name,
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.quantity.formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.unitPrice.formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text('${item.taxRate}%',
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(
                            item.discount != null
                                ? item.discount!.type == DiscountType.fixed
                                    ? CurrencyWidget.value(
                                        context, currency, item.discount!.value)
                                    : '${item.discount!.value}%'
                                : '-',
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(
                            item.totalWithTaxAndDiscount
                                .formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                    ]);
                  }),
                ],
              ),
            ),
          ],
        ),
        if (lineItems.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 6.0),
            child: Text('No items added yet',
                style:
                    textTheme.bodyMedium?.copyWith(color: Palette.placeholder),
                textAlign: TextAlign.left),
          ),
      ],
    );
  }

  Widget _buildSummaryRow(
      BuildContext context, String label, double value, String currencyCode) {
    final textTheme = context.textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
                letterSpacing: 0.1,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 12), // Space between label and value
          Text(
            CurrencyWidget.value(context, currencyCode, value),
            style: textTheme.bodySmall?.copyWith(
              letterSpacing: 0.1,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }
}
