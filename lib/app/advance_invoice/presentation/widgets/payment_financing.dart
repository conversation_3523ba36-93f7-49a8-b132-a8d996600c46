import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/wallet_account.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/action_buttons.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/due_date_picker.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

class PaymentFinancingWidget extends ConsumerStatefulWidget {
  const PaymentFinancingWidget({super.key});

  @override
  ConsumerState<PaymentFinancingWidget> createState() =>
      _PaymentFinancingWidgetState();
}

class _PaymentFinancingWidgetState
    extends ConsumerState<PaymentFinancingWidget> {
  bool showAddBankForm = false;
  SettlementBank? selectedBank;
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _accountNameController = TextEditingController();
  bool _consent = false;
  bool _isValidAccountName = false;
  bool _isValidating = false;
  bool _isSubmitting = false;
  // String? _validationError;
  String? _submitError;
  final bankAccountFormController = ExpandableController();

  @override
  void dispose() {
    _accountNumberController.dispose();
    _accountNameController.dispose();
    super.dispose();
  }

  void _validateAccount() async {
    setState(() {
      _isValidAccountName = false;
      _isValidating = true;
      // _validationError = null;
      _accountNameController.clear();
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    if (bank == null || number.length != 10) {
      setState(() {
        _isValidating = false;
      });
      return;
    }
    final res =
        await ref.read(validateBankAccountUseCaseProvider((number, bank.code)));
    res.when(
      success: (data) {
        setState(() {
          _isValidating = false;
          _isValidAccountName = data.$1.isNotEmpty;
          _accountNameController.text = data.$1;
          // _validationError = data.$1.isEmpty ? 'Account not found' : null;
        });
      },
      failure: (e, _) {
        setState(() {
          _isValidating = false;
          // _validationError = e.toString();
        });
        if (mounted) {
          Toast.apiError(e, context);
        }
      },
    );
  }

  void _submit() async {
    setState(() {
      _isSubmitting = true;
      _submitError = null;
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    final name = _accountNameController.text;
    if (bank == null || number.length != 10 || name.isEmpty || !_consent) {
      setState(() {
        _isSubmitting = false;
        _submitError = 'Please complete all fields.';
      });
      return;
    }
    final params = LinkBankParams(
      number,
      bank.code,
      name,
      bank.name,
      null,
      LinkBankType.terminal,
    );
    final res = await ref.read(linkBankAccountUseCaseProvider(params));
    res.when(
      success: (_) async {
        await ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchBankAccounts(forced: true);
        setState(() {
          _isSubmitting = false;
          showAddBankForm = false;
          _accountNumberController.clear();
          _accountNameController.clear();
          _consent = false;
          _isValidAccountName = false;
          // _validationError = null;
          _submitError = null;
          selectedBank = null;
        });
        if (mounted) {
          Toast.success('Bank account added successfully', context);
        }
      },
      failure: (e, _) {
        setState(() {
          _isSubmitting = false;
          _submitError = e.toString();
        });
      },
    );
  }

  void toggleBankFormVisibility(bool value) {
    setState(() {
      showAddBankForm = value;
      _accountNumberController.clear();
      _accountNameController.clear();
      _consent = false;
      _isValidAccountName = false;
      // _validationError = null;
      _submitError = null;
      selectedBank = null;
    });
  }

  // Helper method to calculate total invoice amount
  double _calculateInvoiceTotal() {
    final createParams = ref.read(createAdvanceInvoiceProvider);
    final lineItems = createParams.lineItems;
    final taxes = createParams.taxes ?? [];
    final charges = createParams.charges ?? [];
    final discount = createParams.discount;

    // Calculate subtotal
    final subtotal = lineItems.fold<double>(0, (sum, item) => sum + item.total);
    final subTotalWithTaxAndDiscount = lineItems.fold<double>(
        0, (sum, item) => sum + item.totalWithTaxAndDiscount);

    // Calculate taxes
    final taxAmount = taxes.fold<double>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charges
    final chargeAmount = charges.fold<double>(0, (sum, charge) {
      return sum + (charge.amount ?? 0);
    });

    // Calculate discount amount
    double discountAmount = 0;
    if (discount != null) {
      if (discount.type == DiscountType.fixed) {
        discountAmount = discount.value.toDouble();
      } else if (discount.type == DiscountType.percentage) {
        discountAmount = subtotal * (discount.value.toDouble() / 100);
      }
    }

    return subTotalWithTaxAndDiscount +
        taxAmount +
        chargeAmount -
        discountAmount;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final state = ref.watch(advanceInvoiceControllerProvider);
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
    final loanContract = state.loanContract.asData?.value;
    final bankAccounts = state.bankAccounts.asData?.value ?? [];
    final settlementBanks = state.settlementBanks.asData?.value ?? [];
    final recipientOutlet = createParams.recipientOutlet;

    // Check if financing is available
    final hasActiveLoanContract = recipientOutlet?.loanContract != null &&
        (recipientOutlet?.loanContract?.isActive ?? false);
    final canApplyForAdvance =
        hasActiveLoanContract && (loanContract?.canApplyForAdvance ?? false);

    // Calculate total invoice amount
    final invoiceTotal = _calculateInvoiceTotal();

    // Check if estimated advance exceeds credit balance
    final creditBalance = recipientOutlet?.loanContract?.creditBalance ?? 0;
    final exceedsCreditBalance =
        hasActiveLoanContract && invoiceTotal > creditBalance;

    // Determine if financing toggle should be enabled
    final canToggleFinancing = canApplyForAdvance && !exceedsCreditBalance;

    final WalletAccount? wallet =
        ref.read(userControllerProvider)?.currentRetailOutlet?.walletAccount;
    final hasWalletAccount =
        wallet?.accountNumber != null && wallet?.bankName != null;
    final walletBank = WalletBank(
        id: 'wallet',
        accountId: 'wallet',
        bankName: wallet?.bankName,
        accountNumber: wallet?.accountNumber,
        bankCode: wallet?.bankCode);
    final disbursementBanks =
        hasWalletAccount ? [walletBank, ...bankAccounts] : bankAccounts;
    final title =
        canApplyForAdvance ? 'Payment & Financing Options' : 'Payment Options';

    return Skeletonizer(
      enabled:
          ref.watch(advanceInvoiceControllerProvider).loanContract.isLoading,
      child: Card(
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: Colors.white,
        elevation: 0,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Palette.stroke),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Text(
                  title,
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    color: Palette.primaryBlack,
                  ),
                ),
                const Gap(16),
                Builder(builder: (context) {
                  final advancedState =
                      ref.watch(advanceInvoiceControllerProvider);
                  final loanContractError =
                      !advancedState.loanContract.isLoading &&
                          advancedState.loanContract.hasError;
                  final bankAccountsError =
                      !advancedState.bankAccounts.isLoading &&
                          advancedState.bankAccounts.hasError;
                  final settlementBankError =
                      !advancedState.settlementBanks.isLoading &&
                          advancedState.settlementBanks.hasError;
                  final errorState = loanContractError ||
                      bankAccountsError ||
                      settlementBankError;
                  final errorObject = advancedState.loanContract.error ??
                      advancedState.bankAccounts.error ??
                      advancedState.settlementBanks.error ??
                      DefaultError('failed to fetch payment data');

                  if (errorState) {
                    return FailureWidget(
                      e: errorObject,
                      retry: () {
                        final notifier =
                            ref.read(advanceInvoiceControllerProvider.notifier);
                        // notifier.fetchAdvanceInvoiceSettings();
                        notifier.fetchLoanContract();
                        notifier.fetchBankAccounts();
                        notifier.fetchSettlementBanks();
                      },
                    );
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (canApplyForAdvance) ...[
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Palette.kF7F7F7,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Palette.stroke),
                          ),
                          child: Row(
                            // mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Apply Financing',
                                      style: textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Palette.primaryBlack,
                                      ),
                                    ),
                                    const Gap(4),
                                    Text(
                                      'Enable this to use TradeDepot financing for this invoice',
                                      // If disabled, you must select a payment account.',
                                      style: textTheme.bodySmall?.copyWith(
                                        color: Palette.blackSecondary,
                                        height: 1.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Gap(16),
                              Switch.adaptive(
                                value: createParams.isAdvance,
                                activeColor: Palette.primary,
                                onChanged: canToggleFinancing
                                    ? (val) {
                                        notifier.toggleFinancing(val);
                                        if (val) {
                                          setState(() {
                                            showAddBankForm = false;
                                          });
                                        }
                                      }
                                    : null,
                              ),
                            ],
                          ),
                        ),
                        // Show credit balance exceeded message
                        if (exceedsCreditBalance) ...[
                          const Gap(12),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Colors.orange.withValues(alpha: 0.3)),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: const Icon(
                                    Icons.warning_amber_rounded,
                                    color: Colors.orange,
                                    size: 20,
                                  ),
                                ),
                                const Gap(12),
                                Expanded(
                                  child: Text(
                                    'Estimated advance exceeds ${recipientOutlet?.outletBusinessName}\'s available ${CurrencyWidget.value(context, createParams.currencyCode, creditBalance)} credit balance',
                                    style: textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color: Colors.orange.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        const Gap(20),
                      ],
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0, -0.1),
                              end: Offset.zero,
                            ).animate(CurvedAnimation(
                              parent: animation,
                              curve: Curves.easeInOut,
                            )),
                            child: FadeTransition(
                              opacity: animation,
                              child: child,
                            ),
                          );
                        },
                        child: createParams.isAdvance
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Disbursement Account',
                                      style: textTheme.bodyMedium),
                                  const Gap(4),
                                  Skeletonizer(
                                    enabled: ref
                                        .watch(advanceInvoiceControllerProvider)
                                        .bankAccounts
                                        .isLoading,
                                    child: SizedBox(
                                      // height: 32,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          // const Gap(16),
                                          Flexible(
                                            flex: 1,
                                            fit: FlexFit.tight,
                                            child: SmoothExpandable(
                                              controller:
                                                  bankAccountFormController,
                                              title: 'Add Bank Account',
                                              initiallyExpanded:
                                                  showAddBankForm,
                                              onExpansionChanged:
                                                  toggleBankFormVisibility,
                                              content: _buildAddBankForm(
                                                context,
                                                textTheme,
                                                settlementBanks,
                                              ),
                                              leading:
                                                  AdvancedDropdown<WalletBank>(
                                                selectedValue: disbursementBanks
                                                        .isNotEmpty
                                                    ? disbursementBanks
                                                        .firstWhereOrNull((bank) =>
                                                            (bank.accountId !=
                                                                    null &&
                                                                bank.accountId ==
                                                                    createParams
                                                                        .disbursementAccountId) ||
                                                            bank.id != null &&
                                                                bank.id ==
                                                                    createParams
                                                                        .disbursementAccountId)
                                                    : null,
                                                options: disbursementBanks,
                                                hint:
                                                    'Select Disbursement Account',
                                                height: 32,
                                                itemToString: (bank) =>
                                                    '${bank.bankName} - ${bank.accountNumber ?? ''}',
                                                onChanged: (val) {
                                                  if (val != null) {
                                                    notifier
                                                        .addDisbursementBank(
                                                            val);
                                                  }
                                                },
                                                searchable:
                                                    bankAccounts.length > 5,
                                                menuMaxHeight: 250,
                                                spaceOffsetAbove:
                                                    bankAccounts.length < 2
                                                        ? 200
                                                        : null,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const Gap(20),
                                  Container(
                                    key: const ValueKey('financing'),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.blue
                                              .withValues(alpha: 0.3)),
                                    ),
                                    child: Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: Colors.blue
                                                .withValues(alpha: 0.1),
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          child: const Icon(
                                            Icons.percent,
                                            color: Colors.blue,
                                            size: 20,
                                          ),
                                        ),
                                        const Gap(12),
                                        Expanded(
                                          child: Text(
                                            loanContract != null
                                                ? 'Your advance rate: ${loanContract.advanceRate ?? 0}%'
                                                : 'Loading advance rate...',
                                            style:
                                                textTheme.bodyMedium?.copyWith(
                                              fontWeight: FontWeight.w500,
                                              color: Colors.blue.shade700,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                            : Container(
                                key: const ValueKey('bank-selection'),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Payment Account',
                                        style: textTheme.bodyMedium),
                                    const Gap(4),
                                    Skeletonizer(
                                      enabled:
                                          advancedState.bankAccounts.isLoading,
                                      child: SizedBox(
                                        // height: 32,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // const Gap(16),
                                            Flexible(
                                              flex: 1,
                                              fit: FlexFit.tight,
                                              child: SmoothExpandable(
                                                controller:
                                                    bankAccountFormController,
                                                title: 'Add Bank Account',
                                                initiallyExpanded:
                                                    showAddBankForm,
                                                onExpansionChanged:
                                                    toggleBankFormVisibility,
                                                content: _buildAddBankForm(
                                                  context,
                                                  textTheme,
                                                  settlementBanks,
                                                ),
                                                leading: AdvancedDropdown<
                                                    WalletBank>(
                                                  selectedValue: bankAccounts
                                                          .isNotEmpty
                                                      ? bankAccounts
                                                          .firstWhereOrNull((bank) =>
                                                              bank.accountNumber ==
                                                              createParams
                                                                  .settlementBank
                                                                  ?.accountNumber)
                                                      : null,
                                                  options: bankAccounts,
                                                  hint:
                                                      'Select Payment Account',
                                                  height: 32,
                                                  itemToString: (bank) =>
                                                      '${bank.bankName} - ${bank.accountNumber ?? ''}',
                                                  onChanged: (val) {
                                                    if (val != null) {
                                                      notifier
                                                          .addSettlementBank(
                                                              val);
                                                    }
                                                  },
                                                  searchable:
                                                      bankAccounts.length > 5,
                                                  menuMaxHeight: 250,
                                                  spaceOffsetAbove:
                                                      bankAccounts.length < 2
                                                          ? 200
                                                          : null,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                      if (!createParams.isAdvance) ...[
                        // Payment Due Date UI
                        const Gap(20),
                        DueDatePickerOverlay(
                          selectedDate: createParams.dueDate,
                          onDateSelected: (date) {
                            notifier.setDueDate(date);
                          },
                          maxMonths: 24,
                          label: 'Payment Due Date',
                        ),
                        const Gap(20),
                      ],
                    ],
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddBankForm(BuildContext context, TextTheme textTheme,
      List<SettlementBank> settlementBanks) {
    return Skeletonizer(
      enabled:
          ref.watch(advanceInvoiceControllerProvider).settlementBanks.isLoading,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.stroke),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add New Bank Account',
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            const Gap(16),
            AdvancedDropdown<SettlementBank>(
              selectedValue: selectedBank,
              options: settlementBanks,
              hint: 'Settlement Bank',
              itemToString: (bank) => bank.name,
              onChanged: (val) {
                setState(() {
                  selectedBank = val;
                });
                if (val != null && _accountNumberController.text.length == 10) {
                  _validateAccount();
                }
              },
              searchable: settlementBanks.length > 5,
              menuMaxHeight: 200,
              height: 34,
            ),
            const Gap(16),
            CompactInput(
              controller: _accountNumberController,
              hint: 'Enter 10-digit account number',
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              onEditingComplete: () {
                if (selectedBank != null &&
                    _accountNumberController.text.length == 10) {
                  _validateAccount();
                }
              },
              onChanged: (val) {
                if (val.length == 10 && selectedBank != null) {
                  _validateAccount();
                }
              },
            ),
            const Gap(16),
            Row(
              children: [
                Expanded(
                  child: CompactInput(
                    controller: _accountNameController,
                    hint: 'Account Name',
                    readOnly: true,
                  ),
                ),
                if (_isValidating)
                  const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
              ],
            ),
            const Gap(16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _consent,
                  onChanged: (val) {
                    setState(() {
                      _consent = val ?? false;
                    });
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
                const Gap(8),
                Expanded(
                  child: Text(
                    'I confirm that this bank account belongs to me and I authorize TradeDepot to verify this account.',
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: Palette.blackSecondary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
            if (_submitError != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  _submitError!,
                  style: textTheme.bodySmall?.copyWith(
                    color: Palette.kE61010,
                  ),
                ),
              ),
            const Gap(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CancelButton(
                  onPressed: _isSubmitting
                      ? null
                      : () {
                          toggleBankFormVisibility(false);
                          bankAccountFormController.toggle();
                        },
                  text: 'Cancel',
                ),
                const Gap(8),
                SaveButton(
                  onPressed: _isSubmitting || !_consent || !_isValidAccountName
                      ? null
                      : _submit,
                  isLoading: _isSubmitting,
                  text: 'Add Account',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
