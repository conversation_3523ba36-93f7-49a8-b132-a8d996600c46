import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/presentation/controllers/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/widgets/action_buttons.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class ItemsWidget extends ConsumerStatefulWidget {
  const ItemsWidget({super.key});
  @override
  ConsumerState<ItemsWidget> createState() => ItemsWidgetState();
}

class ItemsWidgetState extends ConsumerState<ItemsWidget> {
  bool showForm = false;
  bool showDiscountFields = false;
  String? editingItemId;
  final _nameController = TextEditingController();
  final _qtyController = TextEditingController();
  final _priceController = TextEditingController();
  final _taxController = TextEditingController();
  final _discountValueController = TextEditingController();
  DiscountType _selectedDiscountType = DiscountType.fixed;
  final itemsFormController = ExpandableController();
  final _nameFieldFocus = FocusNode();

  @override
  void dispose() {
    _nameController.dispose();
    _qtyController.dispose();
    _priceController.dispose();
    _taxController.dispose();
    _discountValueController.dispose();
    super.dispose();
  }

  void _startEditing(LineItemParam item) {
    if (!itemsFormController.isExpanded) {
      itemsFormController.toggle();
    }

    setState(() {
      editingItemId = item.id;
      _nameController.text = item.name;
      _qtyController.text = item.quantity.toString();
      _priceController.text = item.unitPrice.toString();
      _taxController.text = item.taxRate.toString();
      _discountValueController.text = item.discount?.value.toString() ?? '';
      _selectedDiscountType = item.discount?.type ?? DiscountType.fixed;
      showDiscountFields = item.discount != null;
      showForm = true;
    });

    _nameFieldFocus.unfocus();
  }

  void _saveItem() {
    final name = _nameController.text.trim();
    final quantity =
        int.tryParse(_qtyController.text.replaceAll(',', '').trim()) ?? 0;
    final price =
        double.tryParse(_priceController.text.replaceAll(',', '').trim()) ?? 0;
    final taxRate =
        double.tryParse(_taxController.text.replaceAll(',', '').trim()) ?? 0;

    if (name.isNotEmpty && quantity > 0 && price > 0) {
      // Create discount if value is provided and discount fields are shown
      Discount? discount;
      if (showDiscountFields && _discountValueController.text.isNotEmpty) {
        final value = double.tryParse(
                _discountValueController.text.replaceAll(',', '').trim()) ??
            0;
        if (value > 0 ||
            (_selectedDiscountType == DiscountType.percentage &&
                value >= 0 &&
                value <= 100)) {
          discount = Discount(
            type: _selectedDiscountType,
            value: value,
          );
        }
      }

      if (editingItemId != null) {
        // Update existing item
        final updatedItem = LineItemParam(
          id: editingItemId!,
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateLineItem(updatedItem);
        setState(() {
          editingItemId = null;
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      } else {
        // Add new item
        final newItem = LineItemParam(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addLineItem(newItem);
        setState(() {
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      }
    }

    if (_nameFieldFocus.canRequestFocus) {
      _nameFieldFocus.requestFocus();
    }
  }

  void _cancelEdit() {
    setState(() {
      editingItemId = null;
      showForm = false;
      showDiscountFields = false;
      _nameController.clear();
      _qtyController.clear();
      _priceController.clear();
      _taxController.clear();
      _discountValueController.clear();
    });
  }

  void toggleItemsFormVisibility(bool value) {
    setState(() {
      editingItemId = null;
      showForm = value;
      showDiscountFields = false;
      _nameController.clear();
      _qtyController.clear();
      _priceController.clear();
      _taxController.clear();
      _discountValueController.clear();
    });

    if (_nameFieldFocus.canRequestFocus) {
      _nameFieldFocus.requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final lineItems = ref.watch(createAdvanceInvoiceProvider).lineItems;
    final currencyCode = ref.read(currencyCodeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        // Add Item Section with smooth expansion
        SmoothExpandable(
          controller: itemsFormController,
          title: 'Add Item',
          initiallyExpanded: showForm,
          onExpansionChanged: toggleItemsFormVisibility,
          content: _buildItemForm(context, textTheme, currencyCode),
        ),
        const Gap(16),
        // Items List
        _buildItemsList(context, textTheme, currencyCode, lineItems),
      ],
    );
  }

  Widget _buildItemForm(
    BuildContext context,
    TextTheme textTheme,
    String currencyCode,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Row(
          children: [
            Expanded(
              child: AutocompleteInput(
                controller: _nameController,
                hint: 'Item name',
                autofocus: true,
                focusNode: _nameFieldFocus,
                onSelected: (variant) {
                  if (variant != null) {
                    // Update price field with variant's price
                    _priceController.text =
                        variant.price != null ? variant.price.toString() : '';
                  }
                },
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _qtyController,
                hint: 'Quantity',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatInput()],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _priceController,
                hint: 'Unit Price',
                prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatCurrency(decimalPlaces: 2)],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _taxController,
                hint: 'Tax (%)',
                suffix: '%',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatRange(0, 100)],
                onEditingComplete: () {
                  if (!showDiscountFields) {
                    _saveItem();
                  }
                },
              ),
            ),
          ],
        ),
        const Gap(10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!showDiscountFields)
              Expanded(
                child: Row(
                  // mainAxisSize: MainAxisSize.min,
                  children: [
                    Transform.scale(
                      scale: 0.7,
                      child: Checkbox(
                        value: false,
                        onChanged: (value) {
                          setState(() {
                            showDiscountFields = true;
                          });
                        },
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      ),
                    ),
                    Text(
                      'Add discount',
                      style: textTheme.bodySmall?.copyWith(
                        color: Palette.primary,
                      ),
                    ).onTapNoFeedback(() {
                      setState(() {
                        showDiscountFields = true;
                      });
                    }),
                  ],
                ),
              )
            else ...[
              Expanded(
                flex: 2,
                child: AdvancedDropdown<DiscountType>(
                  selectedValue: _selectedDiscountType,
                  options: DiscountType.values,
                  hint: 'Type',
                  itemToString: (type) =>
                      type == DiscountType.fixed ? 'Fixed' : 'Percentage',
                  onChanged: (type) {
                    if (type != null) {
                      setState(() {
                        _selectedDiscountType = type;
                        _discountValueController.clear();
                      });
                    }
                  },
                  searchable: false,
                  menuMaxHeight: 100,
                  isExpanded: false,
                  height: 34,
                ),
              ),
              const Gap(12),
              Expanded(
                flex: 2,
                child: CompactInput(
                  controller: _discountValueController,
                  hint: _selectedDiscountType == DiscountType.fixed
                      ? 'Amount'
                      : 'Value (%)',
                  prefix: _selectedDiscountType == DiscountType.fixed
                      ? '${CurrencyWidget.symbol(context, currencyCode)} '
                      : null,
                  suffix: _selectedDiscountType == DiscountType.percentage
                      ? '%'
                      : null,
                  keyboardType: TextInputType.number,
                  inputFormatters:
                      _selectedDiscountType == DiscountType.percentage
                          ? [Validators.formatRange(0, 100)]
                          : [Validators.formatCurrency(decimalPlaces: 2)],
                  onEditingComplete: _saveItem,
                ),
              ),
              const Gap(8),
              IconButton(
                icon: const Icon(Icons.close, size: 14),
                onPressed: () {
                  setState(() {
                    showDiscountFields = false;
                    _discountValueController.clear();
                  });
                },
              ),
            ],
            // const Spacer(),
            CancelButton(
              onPressed: () {
                _cancelEdit();
                itemsFormController.toggle();
              },
              text: 'Cancel',
            ),
            const Gap(8),
            SaveButton(
              onPressed: _saveItem,
              text: editingItemId != null ? 'Update' : 'Save',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildItemsList(BuildContext context, TextTheme textTheme,
      String currencyCode, List<LineItemParam> lineItems) {
    if (lineItems.isEmpty) {
      return Center(
        child: Text(
          'No items added yet',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    return Column(
      children: lineItems.mapIndexed((index, item) {
        return ListTile(
          dense: true,
          title: Text(
            item.name,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          subtitle: Text(
            'Qty: ${item.quantity} × ${CurrencyWidget.value(context, currencyCode, item.unitPrice)}',
            style: textTheme.bodySmall?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (item.discount != null)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Palette.blackSecondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    item.discount!.type == DiscountType.fixed
                        ? CurrencyWidget.value(
                            context, currencyCode, item.discount!.value)
                        : '${item.discount!.value}%',
                    style: textTheme.bodySmall?.copyWith(
                      color: Palette.blackSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              Text(
                CurrencyWidget.value(
                    context, currencyCode, (item.quantity * item.unitPrice)),
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
              ),
              const Gap(8),
              IconButton(
                icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                // icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                onPressed: () => _startEditing(item),
                // onPressed: () => _startEditing(item),
              ),
              IconButton(
                icon: SvgPicture.asset('$kSvgDir/order/delete.svg'),
                // icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                onPressed: () {
                  ref
                      .read(advanceInvoiceControllerProvider.notifier)
                      .removeLineItem(item);
                },
              ),
            ],
          ),
          contentPadding: EdgeInsets.zero,
        );
      }).toList(),
    );
  }
}
